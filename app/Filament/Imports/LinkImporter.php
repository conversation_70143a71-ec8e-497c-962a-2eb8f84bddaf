<?php

namespace App\Filament\Imports;

use App\Models\Link;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Support\Number;

class LinkImporter extends Importer
{
    protected static ?string $model = Link::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('id'),
            ImportColumn::make('original_url')
                ->requiredMappingForNewRecordsOnly()
                ->rules(['required', 'max:2048']),
            ImportColumn::make('slug')
                ->rules(['max:255']),
            ImportColumn::make('password')
                ->rules(['max:255']),
            ImportColumn::make('is_active')
                ->boolean()
                ->rules(['boolean']),
            ImportColumn::make('available_at'),
            ImportColumn::make('unavailable_at'),
            ImportColumn::make('forward_query_parameters')
                ->boolean()
                ->rules(['boolean']),
            ImportColumn::make('send_ref_query_parameter')
                ->boolean()
                ->rules(['boolean']),
            ImportColumn::make('description'),
            ImportColumn::make('tags')
                ->relationship(resolveUsing: ['id', 'name'])
                ->multiple(';')
        ];
    }

    public function resolveRecord(): Link
    {
        return Link::firstOrNew([
            'id' => $this->data['id'],
        ]);
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your link import has completed and ' . Number::format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . Number::format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
