<?php

namespace App\Filament\Imports;

use App\Models\Link;
use App\Models\Tag;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Support\Number;

class LinkImporter extends Importer
{
    protected static ?string $model = Link::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('id')
                ->rules(['nullable', 'integer']),

            ImportColumn::make('original_url')
                ->requiredMapping()
                ->rules(['required', 'url', 'max:2048']),

            ImportColumn::make('slug')
                ->rules(['nullable', 'string', 'max:255'])
                ->helperText('Optional custom slug for the short URL'),

            ImportColumn::make('password')
                ->rules(['nullable', 'string', 'max:255'])
                ->helperText('Optional password protection'),

            ImportColumn::make('is_active')
                ->boolean()
                ->rules(['nullable', 'boolean']),

            ImportColumn::make('available_at')
                ->rules(['nullable', 'date'])
                ->helperText('When the link becomes available (optional)'),

            ImportColumn::make('unavailable_at')
                ->rules(['nullable', 'date', 'after:available_at'])
                ->helperText('When the link expires (optional)'),

            ImportColumn::make('forward_query_parameters')
                ->boolean()
                ->rules(['nullable', 'boolean']),

            ImportColumn::make('send_ref_query_parameter')
                ->boolean()
                ->rules(['nullable', 'boolean']),

            ImportColumn::make('description')
                ->rules(['nullable', 'string'])
                ->helperText('Optional description for the link'),

            ImportColumn::make('tags')
                ->relationship(resolveUsing: 'name')
                ->multiple(';')
                ->helperText('Semicolon-separated tag names (e.g., "tag1;tag2;tag3")')
        ];
    }

    public function resolveRecord(): ?Link
    {
        // If ID is provided, try to find existing record
        if (!empty($this->data['id'])) {
            return Link::firstOrNew(['id' => $this->data['id']]);
        }

        // If slug is provided, try to find by short_path (which is generated from slug)
        if (!empty($this->data['slug'])) {
            return Link::where('short_path', $this->data['slug'])->first() ?? new Link();
        }

        // Otherwise create new record
        return new Link();
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your link import has completed and ' . Number::format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . Number::format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
